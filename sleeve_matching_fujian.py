import pandas as pd
import logging
import math
from sleeve_matching import match_sleeve

def normalize_cabinet_number(cabinet_str):
    """
    标准化屏柜编号，将"~"替换为"-"
    :param cabinet_str: 原始屏柜编号字符串
    :return: 标准化后的屏柜编号字符串
    """
    if cabinet_str is None:
        return None

    normalized = str(cabinet_str).strip()
    # 将"~"替换为"-"
    normalized = normalized.replace('~', '-')
    return normalized

def match_sleeve_fujian(wiring_df, wire_count_df, parallel_df, sleeve_spec_path):
    """
    福建项目专用套管识别
    :param wiring_df: 预处理后的屏柜配线套管表 DataFrame
    :param wire_count_df: 导线统计表 DataFrame
    :param parallel_df: 并线统计表 DataFrame
    :param sleeve_spec_path: 套管匹配表路径
    :return: 套管匹配结果 DataFrame
    """
    logger = logging.getLogger('SleeveMatchingFujian')
    fujian_special_results = []
    try:
        logger.info("开始福建项目套管匹配")
        
        # 1. 先处理福建特殊端套管识别
        logger.info("第一步：处理福建特殊端套管识别")
        # 读取套管匹配表
        sleeve_spec = pd.read_excel(sleeve_spec_path)
        # 过滤福建专用规则
        fujian_sleeve = sleeve_spec[(sleeve_spec['工程要求'] == '福建') &
                                   (sleeve_spec['接口要求'] == '非接地铜排') &
                                   (sleeve_spec['套接根数'] == '单')]
        
        logger.info(f"找到福建专用套管规则：{len(fujian_sleeve)}条")
        
        # 字段名推测
        start_col = None
        end_col = None
        color_diameter_col = None
        for col in wiring_df.columns:
            if '导线起点' in col:
                start_col = col
            if '导线终点' in col:
                end_col = col
            if '颜色/线径标识' in col:
                color_diameter_col = col
        cabinet_col = None
        device_type_col = None
        for col in wiring_df.columns:
            if col == '屏柜编号':
                cabinet_col = col
            if '设备类型' in col:
                device_type_col = col
        
        special_count = 0
        normal_end_results = []  # 存储普通端套管结果
        # 逐行查找特殊端
        for idx, row in wiring_df.iterrows():
            start = str(row[start_col]) if start_col else ''
            end = str(row[end_col]) if end_col else ''
            color_diameter = row[color_diameter_col] if color_diameter_col else ''
            
            # 获取屏柜编号、设备类型
            cabinet = normalize_cabinet_number(row[cabinet_col]) if cabinet_col else ''
            device_type = row[device_type_col] if device_type_col else ''

            # 在导线统计表中查找线径，也需要标准化屏柜编号进行比较
            normalized_cabinet = normalize_cabinet_number(cabinet)
            match = wire_count_df[
                (wire_count_df['屏柜编号'].apply(normalize_cabinet_number) == normalized_cabinet) &
                (wire_count_df['设备类型（起点/终点）'] == device_type) &
                (wire_count_df['颜色/线径标识'] == color_diameter)
            ]
            
            if match.empty:
                logger.warning(f'未找到屏柜{cabinet} 设备类型{device_type} 颜色/线径标识{color_diameter}的线径信息')
                continue
                
            diameter = match.iloc[0]['对应线径']
            
            # 在福建专用套管表中查找
            sleeve_row = fujian_sleeve[fujian_sleeve['对应线径'] == diameter]
            if sleeve_row.empty:
                logger.warning(f'未找到线径{diameter}的福建套管匹配规则')
                continue
                
            sleeve = sleeve_row.iloc[0]
            
            # 分析设备类型，确定普通端的接口要求
            device_parts = device_type.split('/')
            normal_end_interface = None
            if len(device_parts) == 2:
                # 找到"端子排"除外的另一个设备类型
                if "端子排" in device_parts[0] and "端子排" not in device_parts[1]:
                    normal_end_interface = "接地铜排" if "接地铜排" in device_parts[1] else "非接地铜排"
                elif "端子排" not in device_parts[0] and "端子排" in device_parts[1]:
                    normal_end_interface = "接地铜排" if "接地铜排" in device_parts[0] else "非接地铜排"
            
            has_special_end = False  # 标记是否有特殊端
            
            # 分别检查起点和终点是否含有UD/ID/JD
            for flag in ['UD', 'ID', 'JD']:
                # 检查起点是否为特殊端
                if flag in start:
                    has_special_end = True
                    # 处理导线起点和终点：以"/"分界，去除":"后面的内容
                    processed_start = start.split(':')[0]
                    processed_end = end.split(':')[0]
                    
                    result = {
                        '屏柜编号': cabinet,
                        '套管名称': sleeve['套管名称'],
                        '套管型号': sleeve['套管型号'],
                        '套管星瀚编码': sleeve['套管星瀚编码'],
                        '套管星空编码': sleeve['套管星空编码'],
                        '单端长度': sleeve['单端长度/米'],
                        '系统单位长度': sleeve['系统单位长度/米'],
                        '色带型号': sleeve['色带型号'],
                        '数量': 1,
                        '来源': f'福建特殊端-起点({flag})'
                    }
                    fujian_special_results.append(result)
                    special_count += 1
                    logger.debug(f'找到起点特殊端: {processed_start} ({flag})')
                
                # 检查终点是否为特殊端
                if flag in end:
                    has_special_end = True
                    # 处理导线起点和终点：以"/"分界，去除":"后面的内容
                    processed_start = start.split(':')[0]
                    processed_end = end.split(':')[0]
                    
                    result = {
                        '屏柜编号': cabinet,
                        '套管名称': sleeve['套管名称'],
                        '套管型号': sleeve['套管型号'],
                        '套管星瀚编码': sleeve['套管星瀚编码'],
                        '套管星空编码': sleeve['套管星空编码'],
                        '单端长度': sleeve['单端长度/米'],
                        '系统单位长度': sleeve['系统单位长度/米'],
                        '色带型号': sleeve['色带型号'],
                        '数量': 1,
                        '来源': f'福建特殊端-终点({flag})'
                    }
                    fujian_special_results.append(result)
                    special_count += 1
                    logger.debug(f'找到终点特殊端: {processed_end} ({flag})')
            
            # 如果有特殊端，为普通端匹配通用套管
            if has_special_end and normal_end_interface:
                # 从通用套管表中匹配普通端套管
                general_sleeve_spec = sleeve_spec[sleeve_spec['工程要求'] != '福建']
                normal_sleeve_match = general_sleeve_spec[
                    (general_sleeve_spec['对应线径'] == diameter) &
                    (general_sleeve_spec['接口要求'] == normal_end_interface) &
                    (general_sleeve_spec['套接根数'] == "单")
                ]
                
                if not normal_sleeve_match.empty:
                    normal_sleeve = normal_sleeve_match.iloc[0]
                    processed_start = start.split(':')[0]
                    processed_end = end.split(':')[0]
                    
                    normal_result = {
                        '屏柜编号': cabinet,
                        '套管名称': normal_sleeve['套管名称'],
                        '套管型号': normal_sleeve['套管型号'],
                        '套管星瀚编码': normal_sleeve['套管星瀚编码'],
                        '套管星空编码': normal_sleeve['套管星空编码'],
                        '单端长度': normal_sleeve['单端长度/米'],
                        '系统单位长度': normal_sleeve['系统单位长度/米'],
                        '色带型号': normal_sleeve['色带型号'],
                        '数量': 1,
                        '来源': f'福建项目普通端-{normal_end_interface}'
                    }
                    normal_end_results.append(normal_result)
                    logger.debug(f'为普通端匹配通用套管: {normal_sleeve["套管名称"]} (接口: {normal_end_interface})')
                else:
                    logger.warning(f'未找到普通端套管匹配: 线径{diameter}, 接口{normal_end_interface}')
        
        logger.info(f"福建特殊端套管识别完成，找到{special_count}个特殊端")
        logger.info(f"福建项目普通端套管匹配完成，找到{len(normal_end_results)}个普通端")
        
        # 2. 调用通用套管匹配处理其他导线
        logger.info("第二步：处理其他导线的套管匹配")
        # 传入parallel_df进行完整的套管匹配，使用"通用"确保不使用福建专用套管
        general_sleeve_df = match_sleeve(wire_count_df, parallel_df, sleeve_spec_path, "通用")
        
        # 3. 合并所有套管结果
        all_results = []
        
        # 添加通用套管结果
        if not general_sleeve_df.empty:
            for _, row in general_sleeve_df.iterrows():
                all_results.append({
                    '屏柜编号': row['屏柜编号'],
                    '套管名称': row['套管名称'],
                    '套管型号': row['套管型号'],
                    '套管星瀚编码': row['套管星瀚编码'],
                    '套管星空编码': row['套管星空编码'],
                    '单端长度': row['套管总长度'] / row['合并数量'] if row['合并数量'] > 0 else 0,  # 反推单端长度
                    '系统单位长度': row['套管总长度'] / row['系统分子'] if row['系统分子'] > 0 else 1,  # 反推系统单位长度
                    '色带型号': row['色带型号'],
                    '数量': row['合并数量'],
                    '来源': row['数据来源']
                })
        
        # 添加福建项目普通端套管结果
        all_results.extend(normal_end_results)
        
        # 添加福建特殊端套管结果
        all_results.extend(fujian_special_results)
        
        logger.info(f"合并所有套管结果，共{len(all_results)}条记录")
        
        # 4. 统一处理和合并
        if all_results:
            result_df = pd.DataFrame(all_results)
            
            # 计算套管总长度和系统分子
            result_df['套管总长度'] = result_df['单端长度'] * result_df['数量']
            result_df['系统分子'] = result_df['套管总长度'] / result_df['系统单位长度']
            
            # 按星瀚编码和数据来源分组合并
            logger.info("开始按星瀚编码和数据来源分组合并")
            final_df = result_df.groupby([
                '屏柜编号', '套管名称', '套管型号', '套管星瀚编码', '套管星空编码', '色带型号', '来源'
            ]).agg({
                '套管总长度': 'sum',
                '系统分子': 'sum',
                '数量': 'sum'
            }).reset_index()
            
            # 对系统分子进行向上取整并保留两位小数
            final_df['系统分子'] = final_df['系统分子'].apply(lambda x: math.ceil(x * 100) / 100.0)
            
            # 重命名字段
            final_df = final_df.rename(columns={'数量': '合并数量', '来源': '数据来源'})
            
            # 返回统一格式的结果
            final_result = final_df[[
                '屏柜编号', '套管名称', '套管型号', '套管星瀚编码', '套管星空编码', 
                '套管总长度', '系统分子', '色带型号', '合并数量', '数据来源'
            ]]
            
            logger.info(f"最终合并结果：{len(final_result)}条记录")
            
            return final_result
        else:
            logger.warning("没有找到任何套管匹配结果")
            return pd.DataFrame()
            
    except Exception as e:
        logger.exception('福建套管识别出错')
        return pd.DataFrame() 