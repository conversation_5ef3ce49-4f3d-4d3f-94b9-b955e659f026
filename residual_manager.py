"""
残值管理模块
处理残值的读取、更新、检查和应用逻辑
"""

import pandas as pd
import os
from decimal import Decimal, localcontext, ROUND_DOWN
from decimal_calculator import to_decimal, to_float
import logging

# 获取日志记录器
logger = logging.getLogger('ResidualManager')


class ResidualManager:
    """残值管理器"""
    
    def __init__(self, residual_file_path=None):
        """
        初始化残值管理器
        :param residual_file_path: 残值文件路径，如果为None则不使用残值文件
        """
        self.residual_file_path = residual_file_path
        self.residual_df = None
        self.material_min_valid_map = {}  # 物料编码 -> 最小有效值的映射
        self.material_name_map = {}  # 物料编码 -> 物料名称的映射
        
        # 加载残值文件
        self._load_residual_file()
    
    def _load_residual_file(self):
        """加载残值文件"""
        if not self.residual_file_path or not os.path.exists(self.residual_file_path):
            # 如果文件不存在，创建空的DataFrame
            self.residual_df = pd.DataFrame(columns=['物料编码', '物料名称', '残值'])
            logger.info("残值文件不存在，创建空的残值表")
            return
        
        try:
            # 读取残值文件
            self.residual_df = pd.read_excel(self.residual_file_path)
            
            # 确保必要的列存在
            required_columns = ['物料编码', '物料名称', '残值']
            for col in required_columns:
                if col not in self.residual_df.columns:
                    self.residual_df[col] = ''
            
            # 转换物料编码为字符串，便于匹配
            self.residual_df['物料编码'] = self.residual_df['物料编码'].astype(str)
            
            # 转换残值为数值类型
            self.residual_df['残值'] = pd.to_numeric(self.residual_df['残值'], errors='coerce').fillna(0.0)
            
            logger.info(f"成功加载残值文件，共{len(self.residual_df)}条记录")
            
        except Exception as e:
            logger.error(f"加载残值文件失败: {e}")
            # 创建空的DataFrame作为备用
            self.residual_df = pd.DataFrame(columns=['物料编码', '物料名称', '残值'])
    
    def load_material_info(self, wire_spec_path=None, sleeve_spec_path=None):
        """
        从配置文件中加载物料信息（最小有效值等）
        :param wire_spec_path: 线材规格定义文件路径
        :param sleeve_spec_path: 套管匹配文件路径
        """
        try:
            # 加载导线物料信息
            if wire_spec_path and os.path.exists(wire_spec_path):
                wire_sheet3 = pd.read_excel(wire_spec_path, sheet_name=2)
                for _, row in wire_sheet3.iterrows():
                    code = str(row.get('物料编码', ''))
                    min_valid = row.get('最小有效值', 0)
                    name = row.get('物料名称', '')
                    if code and pd.notna(min_valid):
                        self.material_min_valid_map[code] = float(min_valid)
                        self.material_name_map[code] = str(name)
                logger.info(f"加载导线物料信息: {len(wire_sheet3)}条")
            
            # 加载套管物料信息
            if sleeve_spec_path and os.path.exists(sleeve_spec_path):
                sleeve_spec = pd.read_excel(sleeve_spec_path, sheet_name='Sheet1')
                for _, row in sleeve_spec.iterrows():
                    code = str(row.get('套管星瀚编码', ''))
                    min_valid = row.get('最小有效值', 0)
                    name = row.get('套管名称', '')
                    if code and pd.notna(min_valid):
                        self.material_min_valid_map[code] = float(min_valid)
                        self.material_name_map[code] = str(name)
                logger.info(f"加载套管物料信息: {len(sleeve_spec)}条")
                
        except Exception as e:
            logger.error(f"加载物料信息失败: {e}")
    
    def add_or_update_residual(self, material_code, material_name, residual_value):
        """
        添加或更新残值
        :param material_code: 物料编码
        :param material_name: 物料名称
        :param residual_value: 残值
        """
        if not material_code or residual_value == 0:
            return
        
        material_code = str(material_code)
        
        # 使用decimal进行精确计算
        with localcontext() as ctx:
            ctx.prec = 28
            ctx.rounding = ROUND_DOWN
            
            residual_decimal = to_decimal(residual_value)
            if residual_decimal is None:
                return
            
            # 查找是否已存在该物料编码
            existing_mask = self.residual_df['物料编码'] == material_code
            
            if existing_mask.any():
                # 如果存在，累加残值
                existing_idx = self.residual_df[existing_mask].index[0]
                current_residual = to_decimal(self.residual_df.at[existing_idx, '残值'])
                if current_residual is not None:
                    new_residual = current_residual + residual_decimal
                    self.residual_df.at[existing_idx, '残值'] = to_float(new_residual)
                    logger.debug(f"更新残值: {material_code} {material_name} 残值从 {to_float(current_residual)} 更新为 {to_float(new_residual)}")
                else:
                    self.residual_df.at[existing_idx, '残值'] = to_float(residual_decimal)
            else:
                # 如果不存在，添加新行
                new_row = pd.DataFrame({
                    '物料编码': [material_code],
                    '物料名称': [material_name],
                    '残值': [to_float(residual_decimal)]
                })
                self.residual_df = pd.concat([self.residual_df, new_row], ignore_index=True)
                logger.debug(f"添加新残值: {material_code} {material_name} 残值 {to_float(residual_decimal)}")
    
    def check_and_apply_residuals(self):
        """
        检查残值是否达到最小有效值，返回可应用的残值信息
        :return: {物料编码: {'应用数量': float, '剩余残值': float, '物料名称': str}, ...}
        """
        applicable_residuals = {}
        
        if self.residual_df.empty:
            return applicable_residuals
        
        with localcontext() as ctx:
            ctx.prec = 28
            ctx.rounding = ROUND_DOWN
            
            for idx, row in self.residual_df.iterrows():
                material_code = str(row['物料编码'])
                material_name = str(row['物料名称'])
                current_residual = to_decimal(row['残值'])
                
                if current_residual is None or current_residual <= 0:
                    continue
                
                # 获取最小有效值
                min_valid = self.material_min_valid_map.get(material_code, 0)
                if min_valid <= 0:
                    continue
                
                min_valid_decimal = to_decimal(min_valid)
                if min_valid_decimal is None:
                    continue
                
                # 检查是否达到最小有效值
                if current_residual >= min_valid_decimal:
                    # 计算可应用的数量（最小有效值的整数倍）
                    apply_count = int(current_residual / min_valid_decimal)
                    apply_amount = min_valid_decimal * apply_count
                    remaining_residual = current_residual - apply_amount
                    
                    applicable_residuals[material_code] = {
                        '应用数量': to_float(apply_amount),
                        '剩余残值': to_float(remaining_residual),
                        '物料名称': material_name
                    }
                    
                    # 更新残值表中的残值
                    self.residual_df.at[idx, '残值'] = to_float(remaining_residual)
                    
                    logger.info(f"物料 {material_code} {material_name} 残值达到最小有效值，应用数量: {to_float(apply_amount)}, 剩余残值: {to_float(remaining_residual)}")
        
        return applicable_residuals
    
    def save_residual_file(self):
        """保存残值文件"""
        if not self.residual_file_path:
            return

        try:
            # 确保输出目录存在
            dir_path = os.path.dirname(self.residual_file_path)
            if dir_path:  # 只有当目录路径不为空时才创建
                os.makedirs(dir_path, exist_ok=True)
            
            # 转换物料编码为数字格式（如果可能）
            def convert_to_number(code):
                if code is None or pd.isna(code):
                    return code
                try:
                    return int(float(str(code).strip()))
                except (ValueError, TypeError):
                    return code
            
            save_df = self.residual_df.copy()
            save_df['物料编码'] = save_df['物料编码'].apply(convert_to_number)
            
            # 保存到Excel文件
            with pd.ExcelWriter(self.residual_file_path, engine='openpyxl') as writer:
                save_df.to_excel(writer, sheet_name='残值量', index=False)
            
            logger.info(f"残值文件已保存: {self.residual_file_path}")
            
        except Exception as e:
            logger.error(f"保存残值文件失败: {e}")
    
    def get_residual_summary(self):
        """获取残值汇总信息"""
        if self.residual_df.empty:
            return "残值表为空"
        
        total_items = len(self.residual_df)
        non_zero_items = len(self.residual_df[self.residual_df['残值'] > 0])
        
        return f"残值表共{total_items}项物料，其中{non_zero_items}项有残值"


def process_residual_list(residual_list, residual_manager):
    """
    处理残值列表，将残值添加到残值管理器中
    :param residual_list: 残值列表，每个元素包含物料编码、物料名称、残值
    :param residual_manager: 残值管理器实例
    """
    if not residual_list or not residual_manager:
        return
    
    for residual_item in residual_list:
        if isinstance(residual_item, dict):
            material_code = residual_item.get('物料编码', '')
            material_name = residual_item.get('物料名称', '')
            residual_value = residual_item.get('残值', 0)
            
            if material_code and residual_value != 0:
                residual_manager.add_or_update_residual(material_code, material_name, residual_value)
