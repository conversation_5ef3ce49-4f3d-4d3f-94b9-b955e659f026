# multithreading_processor.py

import pandas as pd
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from functools import wraps
import logging

# 导入日志配置
from logger_config import get_main_logger, log_function_start, log_function_end, log_function_error, log_process_step

# 获取日志记录器
logger = get_main_logger()

def normalize_cabinet_number(cabinet_str):
    """
    标准化屏柜编号，将"~"替换为"-"
    :param cabinet_str: 原始屏柜编号字符串
    :return: 标准化后的屏柜编号字符串
    """
    if cabinet_str is None:
        return None

    normalized = str(cabinet_str).strip()
    # 将"~"替换为"-"
    normalized = normalized.replace('~', '-')
    return normalized

# 线程安全的进度回调包装器
class ThreadSafeProgressCallback:
    def __init__(self, original_callback):
        self.original_callback = original_callback
        self.lock = threading.Lock()
    
    def __call__(self, message, current, total):
        if self.original_callback:
            with self.lock:
                self.original_callback(message, current, total)

def parallel_file_reader(file_paths_dict):
    """
    并行读取多个Excel文件
    :param file_paths_dict: 文件路径字典 {文件名: 文件路径}
    :return: 读取结果字典 {文件名: DataFrame}
    """
    def read_single_file(file_info):
        file_name, file_path = file_info
        try:
            log_process_step(logger, f"并行读取文件", f"{file_name}: {file_path}")
            if file_name == "屏柜配线套管":
                # 屏柜配线套管表需要跳过前两行
                df = pd.read_excel(file_path, header=2)
            else:
                df = pd.read_excel(file_path)
            return file_name, df
        except Exception as e:
            log_function_error(logger, f"读取文件{file_name}", str(e))
            return file_name, None
    
    results = {}
    with ThreadPoolExecutor(max_workers=min(len(file_paths_dict), 4)) as executor:
        future_to_file = {executor.submit(read_single_file, item): item[0] 
                         for item in file_paths_dict.items()}
        
        for future in as_completed(future_to_file):
            file_name = future_to_file[future]
            try:
                name, df = future.result()
                results[name] = df
                log_process_step(logger, f"文件读取完成", name)
            except Exception as e:
                log_function_error(logger, f"并行读取文件{file_name}", str(e))
                results[file_name] = None
    
    return results

def parallel_wire_length_calculation(parallel_df, wire_length_def_path):
    """
    并行计算并线统计表的单根长度1和单根长度2
    :param parallel_df: 并线统计表DataFrame
    :param wire_length_def_path: 线长定义文件路径
    :return: 添加了单根长度列的DataFrame
    """
    if parallel_df.empty:
        return parallel_df
    
    from wire_length_processor import add_wire_length
    
    def calculate_length_1(df):
        """计算单根长度1"""
        df_copy = df.copy()
        df_copy['设备类型（起点/终点）'] = df_copy['设备类型1']
        result_df = add_wire_length(df_copy, wire_length_def_path)
        return result_df['单根长度']
    
    def calculate_length_2(df):
        """计算单根长度2"""
        df_copy = df.copy()
        df_copy['设备类型（起点/终点）'] = df_copy['设备类型2']
        result_df = add_wire_length(df_copy, wire_length_def_path)
        return result_df['单根长度']
    
    result_df = parallel_df.copy()
    
    with ThreadPoolExecutor(max_workers=2) as executor:
        # 并行计算两个长度
        future_length1 = executor.submit(calculate_length_1, result_df)
        future_length2 = executor.submit(calculate_length_2, result_df)
        
        # 获取结果
        result_df['单根长度1'] = future_length1.result()
        result_df['单根长度2'] = future_length2.result()
    
    log_process_step(logger, "并行计算单根长度完成")
    return result_df

def parallel_matching_operations(wire_count_df, parallel_df, terminal_def, sleeve_def, 
                                printer_df, power_wire_df, project_type="默认"):
    """
    并行执行压头匹配和套管匹配
    :param wire_count_df: 导线统计DataFrame
    :param parallel_df: 并线统计DataFrame
    :param terminal_def: 压头定义文件路径
    :param sleeve_def: 套管定义文件路径
    :param printer_df: 数据线记录DataFrame
    :param power_wire_df: 电源线记录DataFrame
    :param project_type: 项目类型
    :return: (压头匹配结果, 套管匹配结果, 套管残值列表)
    """
    from terminal_matching import match_terminals
    from sleeve_matching import match_sleeve
    from sleeve_matching_fujian import match_sleeve_fujian
    
    def terminal_matching_task():
        """压头匹配任务"""
        try:
            log_process_step(logger, "开始并行压头匹配")
            result = match_terminals(wire_count_df, parallel_df, terminal_def, printer_df, power_wire_df)
            log_process_step(logger, "并行压头匹配完成")
            return result
        except Exception as e:
            log_function_error(logger, "并行压头匹配", str(e))
            raise
    
    def sleeve_matching_task():
        """套管匹配任务"""
        try:
            log_process_step(logger, "开始并行套管匹配")
            if project_type == "福建":
                # 需要原始配线数据，这里需要传入
                # 暂时使用普通匹配，后续可以优化
                result = match_sleeve(wire_count_df, parallel_df, sleeve_def, project_type)
            else:
                result = match_sleeve(wire_count_df, parallel_df, sleeve_def, project_type)
            log_process_step(logger, "并行套管匹配完成")
            return result
        except Exception as e:
            log_function_error(logger, "并行套管匹配", str(e))
            raise
    
    with ThreadPoolExecutor(max_workers=2) as executor:
        # 并行执行两个匹配任务
        terminal_future = executor.submit(terminal_matching_task)
        sleeve_future = executor.submit(sleeve_matching_task)
        
        # 获取结果
        terminal_result = terminal_future.result()
        sleeve_result = sleeve_future.result()
    
    # 解包结果
    if len(terminal_result) == 2:
        terminal_counts_df, terminal_match_log = terminal_result
    else:
        terminal_counts_df = terminal_result
        terminal_match_log = None
    
    if len(sleeve_result) == 2:
        sleeve_counts_df, sleeve_residual_list = sleeve_result
    else:
        sleeve_counts_df = sleeve_result
        sleeve_residual_list = []
    
    return terminal_counts_df, sleeve_counts_df, sleeve_residual_list

def parallel_data_processing_by_cabinet(df, processing_func, *args, **kwargs):
    """
    按屏柜编号分片并行处理数据
    :param df: 输入DataFrame
    :param processing_func: 处理函数
    :param args: 处理函数的位置参数
    :param kwargs: 处理函数的关键字参数
    :return: 处理后的DataFrame
    """
    if df.empty or '屏柜编号' not in df.columns:
        return processing_func(df, *args, **kwargs)

    # 标准化屏柜编号后按屏柜编号分组
    df_copy = df.copy()
    df_copy['屏柜编号'] = df_copy['屏柜编号'].apply(normalize_cabinet_number)
    cabinet_groups = df_copy.groupby('屏柜编号')
    
    def process_cabinet_group(group_data):
        """处理单个屏柜的数据"""
        cabinet_num, group_df = group_data
        try:
            log_process_step(logger, f"并行处理屏柜{cabinet_num}数据")
            result = processing_func(group_df, *args, **kwargs)
            return result
        except Exception as e:
            log_function_error(logger, f"处理屏柜{cabinet_num}数据", str(e))
            return group_df  # 返回原数据
    
    # 并行处理各个屏柜的数据
    max_workers = min(len(cabinet_groups), 4)  # 限制最大线程数
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(process_cabinet_group, group_data) 
                  for group_data in cabinet_groups]
        
        results = []
        for future in as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                log_function_error(logger, "并行数据处理", str(e))
    
    # 合并结果
    if results:
        final_result = pd.concat(results, ignore_index=True)
        log_process_step(logger, "并行数据处理完成", f"处理了{len(results)}个屏柜")
        return final_result
    else:
        return df

# 性能监控装饰器
def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        log_process_step(logger, f"开始执行{func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            log_process_step(logger, f"{func.__name__}执行完成", f"耗时{execution_time:.2f}秒")
            return result
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            log_function_error(logger, f"{func.__name__}执行失败", f"耗时{execution_time:.2f}秒, 错误: {str(e)}")
            raise
    
    return wrapper

def parallel_bom_generation(unique_positions, bom_generation_func, *args, **kwargs):
    """
    并行生成多个屏柜的BOM文件
    :param unique_positions: 屏柜号列表
    :param bom_generation_func: 单个屏柜BOM生成函数
    :param args: 传递给生成函数的位置参数
    :param kwargs: 传递给生成函数的关键字参数
    :return: (生成的文件列表, 残值列表)
    """
    def generate_single_bom(position):
        """生成单个屏柜的BOM"""
        try:
            log_process_step(logger, f"并行生成屏柜{position}的BOM")
            result = bom_generation_func(position, *args, **kwargs)
            log_process_step(logger, f"屏柜{position}的BOM生成完成")
            return result
        except Exception as e:
            log_function_error(logger, f"生成屏柜{position}的BOM", str(e))
            return None, []

    # 过滤掉包含"FS"的屏柜号
    valid_positions = [pos for pos in unique_positions if "FS" not in str(pos)]

    if not valid_positions:
        log_process_step(logger, "没有有效的屏柜号，跳过BOM生成")
        return [], []

    log_process_step(logger, f"开始并行生成{len(valid_positions)}个屏柜的BOM")

    # 限制最大线程数，避免过多的文件并发操作
    max_workers = min(len(valid_positions), 3)  # BOM生成涉及大量文件操作，限制线程数

    all_output_files = []
    all_residual_list = []

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_position = {
            executor.submit(generate_single_bom, position): position
            for position in valid_positions
        }

        # 收集结果
        for future in as_completed(future_to_position):
            position = future_to_position[future]
            try:
                result = future.result()
                if result:
                    output_file, residual_list = result
                    if output_file:
                        all_output_files.append(output_file)
                    if residual_list:
                        all_residual_list.extend(residual_list)
                    log_process_step(logger, f"屏柜{position}BOM处理成功")
                else:
                    log_function_error(logger, f"屏柜{position}BOM生成", "返回结果为空")
            except Exception as e:
                log_function_error(logger, f"屏柜{position}BOM生成异常", str(e))

    log_process_step(logger, f"并行BOM生成完成", f"成功生成{len(all_output_files)}个文件")
    return all_output_files, all_residual_list

# 供外部调用
__all__ = [
    'parallel_file_reader',
    'parallel_wire_length_calculation',
    'parallel_matching_operations',
    'parallel_data_processing_by_cabinet',
    'parallel_bom_generation',
    'ThreadSafeProgressCallback',
    'performance_monitor'
]
