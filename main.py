# main.py

from excel_file_selector import ExcelFileSelectorWithContinue
import tkinter as tk
from tkinter import messagebox
from wire_count_processor import process_wire_count, merge_power_wire_records
from wire_length_processor import process_wire_length, add_wire_length, add_parallel_wire_length
from parallel_wire_statistics import find_parallel_wires, process_special_wires_in_parallel
from wire_code_matcher import add_wire_code, add_parallel_wire_code, add_power_wire_code
from preprocess_wiring_table import preprocess_wiring_table
from wire_code_matcher import match_printer_wire_code
from wire_diameter_matcher import add_wire_diameter
from terminal_matching  import match_terminals
from sleeve_matching import match_sleeve
from sleeve_matching_fujian import match_sleeve_fujian
from color_band_processor import process_color_band_usage
from design_doc_processor import process_design_document_auto
from bom_processor import process_bom_from_design_doc
from bom_processor import truncate_to_decimals
from short_wire_processor import identify_short_wires
from residual_manager import ResidualManager, process_residual_list
from multithreading_processor import (
    parallel_file_reader,
    parallel_matching_operations,
    ThreadSafeProgressCallback,
    performance_monitor
)
import os
import pandas as pd
import openpyxl
try:
    from pandas.io.excel import ExcelWriter as PandasExcelWriter
except ImportError:
    try:
        PandasExcelWriter = pd.ExcelWriter
    except AttributeError:
        # 对于较新版本的pandas，直接使用pd.ExcelWriter
        PandasExcelWriter = pd.ExcelWriter

# 导入日志配置
from logger_config import get_main_logger, log_function_start, log_function_end, log_function_error, log_process_step, log_file_operation, log_data_info

# 获取主程序日志记录器
logger = get_main_logger()


def get_versioned_filename(file_path):
    """
    获取带版本号的文件名
    :param file_path: 原始文件路径
    :return: 带版本号的新文件路径
    """
    base, ext = os.path.splitext(file_path)
    version = 1

    while True:
        new_path = f"{base}_V{version}{ext}"
        if not os.path.exists(new_path):
            return new_path
        version += 1


def check_versions():
    pd_version = pd.__version__
    oxl_version = openpyxl.__version__
    logger.info(f"pandas 版本: {pd_version}, openpyxl 版本: {oxl_version}")
    if int(pd_version.split('.')[0]) >= 2 and int(oxl_version.split('.')[0]) < 3:
        logger.warning("建议升级 openpyxl 至 3.x 以上以兼容 pandas 2.x")


@performance_monitor
def handle_selection(input_files, output_path_or_dir, material_type=None, cable_type=None, project_type=None, interface_mode=None, generate_output_only=False, app=None, progress_callback=None, save_residual_table=True, use_multithreading=True):
    try:
        log_function_start(logger, "handle_selection", 界面模式=interface_mode, 项目类型=project_type, 多线程=use_multithreading)

        # 包装进度回调为线程安全版本
        if use_multithreading and progress_callback:
            progress_callback = ThreadSafeProgressCallback(progress_callback)

        # 处理自动识别模式的参数
        if interface_mode == "自动识别":
            log_process_step(logger, "启动自动识别模式")
            
            # 如果是自动识别模式，需要检查是否有设计说明书
            design_doc_file = input_files.get("设计说明书")
            if not design_doc_file:
                raise ValueError("自动识别模式需要提供设计说明书文件")
            
            # 使用设计说明书处理模块自动识别参数
            if material_type == "自动识别" or cable_type == "自动识别":
                log_process_step(logger, "处理设计说明书", design_doc_file)
                
                # 定义GUI切换回调函数
                def switch_to_manual(mode=None):
                    """切换到手动选择模式的回调函数

                    Args:
                        mode (str, optional): 切换模式，默认为None
                    """
                    if app and hasattr(app, 'notebook'):
                        app.notebook.select(0)  # 切换到手动选择标签页
                        app.interface_mode = "手动选择"
                        app.status_var.set("已切换到手动选择模式，请设置线材规格和线径选型")
                
                # 处理设计说明书
                auto_material, auto_cable, is_rotating_cabinet, small_busbar_data, cabinet_type, success = process_design_document_auto(
                    design_doc_file, 
                    gui_callback=switch_to_manual
                )
                
                if success:
                    material_type = auto_material
                    cable_type = auto_cable
                    rotation_info = "旋转柜（导线长度×2）" if is_rotating_cabinet else "普通柜"
                    log_process_step(logger, "自动识别成功", f"线材规格={material_type}, 线径选型={cable_type}, 柜体类型={rotation_info}")
                else:
                    # 自动识别失败，停止处理
                    log_function_error(logger, "自动识别", "识别失败，切换到手动模式")
                    if app and hasattr(app, 'status_var'):
                        app.status_var.set("自动识别失败，已切换到手动选择模式")
                    return []
        else:
            log_process_step(logger, "手动选择模式", f"线材规格={material_type}, 线径选型={cable_type}, 项目类型={project_type}")
        
        # 根据模式处理输出路径
        if generate_output_only:
            # 生成输出表模式：output_path_or_dir 是具体的文件路径
            output_path = output_path_or_dir
            output_dir = os.path.dirname(output_path)
            log_process_step(logger, "生成输出表模式", output_path)
        else:
            # BOM清单生成模式：output_path_or_dir 是输出目录
            output_dir = output_path_or_dir
            log_process_step(logger, "BOM清单生成模式", output_dir)
        
        # 检查输出目录是否有效
        if not output_dir:
            raise ValueError("输出目录不能为空")
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 获取文件路径
        wiring_file = input_files["屏柜配线套管"]
        wire_length_def = input_files["线长定义"]
        wire_spec_def = input_files["线材规格定义"]
        terminal_def = input_files["压头匹配"]
        sleeve_def = input_files["套管匹配"]
        residual_file = input_files.get("残值")  # 残值文件是可选的
        
        # 验证必要的文件是否存在
        required_files = {
            "屏柜配线套管": wiring_file,
            "线长定义": wire_length_def,
            "线材规格定义": wire_spec_def,
            "压头匹配": terminal_def,
            "套管匹配": sleeve_def
        }
        
        for file_name, file_path in required_files.items():
            if not file_path or not os.path.exists(file_path):
                raise ValueError(f"缺少必要的文件: {file_name}")
        
        log_process_step(logger, "文件验证完成", "所有必要文件已验证")

        # 1. 首先预处理屏柜配线套管表
        log_process_step(logger, "开始预处理屏柜配线套管表")
        if progress_callback:
            progress_callback("正在预处理屏柜配线套管表...", 1, 15)
        wiring_df = preprocess_wiring_table(wiring_file)
        log_data_info(logger, "预处理后数据", len(wiring_df))

        # 1.5. 识别短接线
        log_process_step(logger, "开始识别短接线")
        if progress_callback:
            progress_callback("正在识别短接线...", 2, 15)
        short_wires_df, filtered_wiring_df = identify_short_wires(wiring_df)
        log_data_info(logger, "短接线识别", len(short_wires_df))
        log_data_info(logger, "过滤后导线数据", len(filtered_wiring_df))

        # 2. 使用过滤后的数据进行并线统计
        log_process_step(logger, "执行并线统计")
        if progress_callback:
            progress_callback("正在执行并线统计...", 3, 15)
        parallel_df = find_parallel_wires(filtered_wiring_df)
        parallel_df = process_special_wires_in_parallel(parallel_df, wire_spec_def, material_type, cable_type)
        parallel_df, parallel_code_info = add_parallel_wire_code(parallel_df, wire_spec_def, material_type, cable_type)
        log_data_info(logger, "并线统计", len(parallel_df))

        # === 为并线统计表添加单根长度 ===
        log_process_step(logger, "为并线统计表添加单根长度")
        parallel_df = add_parallel_wire_length(parallel_df, wire_length_def, use_multithreading)

        # 3. 收集并线统计中涉及的导线索引
        parallel_indices = set()
        if not parallel_df.empty:
            parallel_indices.update(parallel_df['索引1'].tolist())
            parallel_indices.update(parallel_df['索引2'].tolist())
            log_process_step(logger, "并线索引处理", f"涉及{len(parallel_indices)}个索引")
        else:
            log_process_step(logger, "并线索引处理", "未找到并线导线")

        # 4. 排除并线统计中的导线
        filtered_wiring_df['原始索引'] = filtered_wiring_df.index
        filtered_wiring_df = filtered_wiring_df[~filtered_wiring_df['原始索引'].isin(parallel_indices)].copy()
        log_process_step(logger, "导线过滤", f"原始{len(filtered_wiring_df)}条 → 过滤后{len(filtered_wiring_df)}条")

        # 5. 使用过滤后的数据进行导线根数统计
        log_process_step(logger, "开始导线根数统计")
        if progress_callback:
            progress_callback("正在统计导线根数...", 4, 15)
        wire_count_df, printer_df, power_wire_df = process_wire_count(filtered_wiring_df)
        log_data_info(logger, "导线统计", len(wire_count_df))
        log_data_info(logger, "数据线记录", len(printer_df))
        log_data_info(logger, "电源线记录", len(power_wire_df))

        # 6. 添加编码信息
        log_process_step(logger, "添加编码信息")
        if progress_callback:
            progress_callback("正在添加编码信息...", 5, 15)
        power_wire_df = add_power_wire_code(power_wire_df, wire_spec_def, material_type)
        printer_df = match_printer_wire_code(printer_df, wire_spec_def, material_type)
        
        # 6.5. 合并电源线记录
        log_process_step(logger, "合并电源线记录")
        power_wire_df = merge_power_wire_records(power_wire_df)
        log_data_info(logger, "合并后电源线记录", len(power_wire_df))
        
        # 7. 添加对应线径信息
        log_process_step(logger, "添加线径信息")
        if progress_callback:
            progress_callback("正在添加线径信息...", 6, 15)
        wire_count_df = add_wire_diameter(wire_count_df, input_files["线材规格定义"], material_type, cable_type)

        # 8. 处理导线线长
        log_process_step(logger, "计算导线长度")
        if progress_callback:
            progress_callback("正在计算导线长度...", 7, 15)
        rotating_cabinet_factor = locals().get('is_rotating_cabinet', False)
        wire_count_df = process_wire_length(wire_count_df, wire_length_def, rotating_cabinet_factor)

        # 9. 添加编码列
        wire_count_df, wire_code_info = add_wire_code(wire_count_df, wire_spec_def, material_type, cable_type)

        # 10-11. 并行执行压头匹配和套管匹配
        if use_multithreading:
            log_process_step(logger, "并行执行压头匹配和套管匹配")
            if progress_callback:
                progress_callback("正在并行执行压头匹配和套管匹配...", 8, 15)
            terminal_counts_df, sleeve_counts_df, sleeve_residual_list = parallel_matching_operations(
                wire_count_df, parallel_df, terminal_def, sleeve_def,
                printer_df, power_wire_df, project_type
            )
            log_data_info(logger, "压头匹配结果", len(terminal_counts_df))
            log_data_info(logger, "套管匹配结果", len(sleeve_counts_df))
        else:
            # 10. 执行压头匹配
            log_process_step(logger, "执行压头匹配")
            if progress_callback:
                progress_callback("正在执行压头匹配...", 8, 15)
            from terminal_matching import match_terminals
            terminal_counts_df, terminal_match_log = match_terminals(wire_count_df, parallel_df, terminal_def, printer_df, power_wire_df)
            log_data_info(logger, "压头匹配结果", len(terminal_counts_df))

            # 11. 执行套管匹配
            log_process_step(logger, "执行套管匹配", f"项目类型={project_type}")
            if progress_callback:
                progress_callback("正在执行套管匹配...", 9, 15)
            if project_type == "福建":
                from sleeve_matching_fujian import match_sleeve_fujian
                sleeve_counts_df, sleeve_residual_list = match_sleeve_fujian(wiring_df, wire_count_df, parallel_df, sleeve_def)
            else:
                from sleeve_matching import match_sleeve
                sleeve_counts_df, sleeve_residual_list = match_sleeve(wire_count_df, parallel_df, sleeve_def, project_type)
            log_data_info(logger, "套管匹配结果", len(sleeve_counts_df))

        # 12. 执行色带用量统计
        log_process_step(logger, "计算色带用量")
        if progress_callback:
            progress_callback("正在计算色带用量...", 10, 15)
        color_band_df = process_color_band_usage(sleeve_counts_df, sleeve_def)
        log_data_info(logger, "色带用量统计", len(color_band_df))

        # 13. 初始化残值管理器
        log_process_step(logger, "初始化残值管理器")
        residual_manager = ResidualManager(residual_file)

        # 加载物料信息（最小有效值等）
        residual_manager.load_material_info(wire_spec_def, sleeve_def)

        # 处理套管残值数据
        if sleeve_residual_list:
            process_residual_list(sleeve_residual_list, residual_manager)
            log_process_step(logger, "处理套管残值", f"处理了{len(sleeve_residual_list)}项残值")

        # 14. 处理完成，准备生成BOM清单
        log_process_step(logger, "数据处理完成，准备生成BOM清单")
        if progress_callback:
            progress_callback("数据处理完成，准备生成输出...", 11, 15)

        # 15. 处理BOM清单（仅在自动识别模式下，且不是纯输出表模式）
        small_busbar_data = locals().get('small_busbar_data')
        if (not generate_output_only and 
            interface_mode == "自动识别"):
            
            # 检查是否有BOM模板和自备料库文件
            bom_template_file = input_files.get("BOM模板") if input_files else None
            material_library_file = input_files.get("自备料库") if input_files else None
            cabinet_bom_file = input_files.get("屏柜BOM表") if input_files else None
            
            if bom_template_file and material_library_file and os.path.exists(bom_template_file) and os.path.exists(material_library_file):
                log_process_step(logger, "处理BOM清单")
                if progress_callback:
                    progress_callback("正在处理BOM清单...", 12, 15)
                
                # 获取柜式要求信息
                cabinet_type_info = locals().get('cabinet_type', 'unknown')
                
                # 获取物料辅料与把手文件
                material_list_file = input_files.get("物料辅料与把手") if input_files else None
                
                # 如果没有小母线数据，创建空的小母线数据结构
                if not small_busbar_data or not (
                    (small_busbar_data.get('bracket', {}) or {}).get('layout') or 
                    (small_busbar_data.get('copper', {}) or {}).get('spec')
                ):
                    log_process_step(logger, "无小母线数据，创建空的小母线数据结构")
                    small_busbar_data = {
                        'bracket': {'layout': None, 'quantity': None},
                        'copper': {'spec': None, 'quantity': None}
                    }
                
                # 处理BOM清单，直接传递DataFrame数据
                wire_loss_info = {**wire_code_info, **parallel_code_info}
                bom_files, residual_list = process_bom_from_design_doc(
                    bom_template_file,
                    material_library_file,
                    small_busbar_data,
                    cabinet_type_info,
                    output_dir,  # 传递输出目录
                    cabinet_bom_file,
                    wire_count_df,  # 传递导线统计数据
                    parallel_df,    # 传递并线统计数据
                    printer_df,     # 传递数据线记录表
                    power_wire_df,  # 传递电源线记录表
                    terminal_counts_df,  # 传递压头匹配数据
                    sleeve_counts_df,    # 传递套管匹配数据
                    color_band_df,       # 新增：传递色带用量数据
                    material_list_file,  # 新增：传递物料辅料与把手文件
                    wire_loss_info=wire_loss_info,
                    decimal_places_func=truncate_to_decimals,
                    residual_manager=residual_manager,  # 新增：传递残值管理器
                    use_multithreading=use_multithreading  # 新增：多线程支持
                )
                # 新增：处理BOM残值数据
                if residual_list:
                    process_residual_list(residual_list, residual_manager)
                    log_process_step(logger, "处理BOM残值", f"处理了{len(residual_list)}项残值")
                
                if bom_files:
                    log_process_step(logger, f"BOM清单处理完成，生成了{len(bom_files)}个文件:")
                    for bom_file in bom_files:
                        log_process_step(logger, "生成BOM文件", bom_file)
                    if cabinet_bom_file and os.path.exists(cabinet_bom_file):
                        log_process_step(logger, "项目信息已从屏柜BOM表填入", cabinet_bom_file)
                    
                    # BOM清单生成完成
                    if progress_callback:
                        progress_callback("BOM清单处理完成！", 15, 15)
                    if app and hasattr(app, 'status_var'):
                        app.status_var.set(f"BOM清单处理完成！共生成{len(bom_files)}个文件")
                    if app and hasattr(app, 'add_continue_button'):
                        app.add_continue_button()
                    # BOM清单生成成功，继续处理残值数据
                    pass
                else:
                    log_process_step(logger, "BOM清单处理失败")
            else:
                log_process_step(logger, "跳过BOM处理", "缺少BOM模板或自备料库文件")
        else:
            if generate_output_only:
                log_process_step(logger, "跳过BOM处理", "输出表生成模式")
                # 生成输出表前检查版本和路径
                check_versions()
                log_process_step(logger, "开始生成输出表")
                if progress_callback:
                    progress_callback("正在生成输出表...", 12, 15)
                try:
                    if not isinstance(output_path, str) or not os.path.isdir(os.path.dirname(output_path)):
                        raise ValueError(f"输出路径无效: {output_path}")
                    
                    # 使用 pandas 的 to_excel 方法，避免 ExcelWriter 兼容性问题
                    # 创建ExcelWriter对象
                    try:
                        writer = pd.ExcelWriter(output_path, engine='openpyxl')
                    except Exception as e:
                        logger.warning(f"使用openpyxl引擎失败，尝试使用xlsxwriter: {e}")
                        writer = pd.ExcelWriter(output_path, engine='xlsxwriter')
                    
                    # 保存短接线表
                    if not short_wires_df.empty:
                        short_wires_df.to_excel(writer, sheet_name='短接线', index=False)
                        log_data_info(logger, "短接线表", len(short_wires_df))
                    # 保存导线统计表
                    if not wire_count_df.empty:
                        wire_count_df.to_excel(writer, sheet_name='导线统计', index=False)
                        log_data_info(logger, "导线统计表", len(wire_count_df))
                    # 保存并线统计表
                    if not parallel_df.empty:
                        parallel_df.to_excel(writer, sheet_name='并线统计', index=False)
                        log_data_info(logger, "并线统计表", len(parallel_df))
                    # 保存数据线记录表
                    if not printer_df.empty:
                        printer_df.to_excel(writer, sheet_name='数据线记录', index=False)
                        log_data_info(logger, "数据线记录表", len(printer_df))
                    # 保存电源线记录表
                    if not power_wire_df.empty:
                        power_wire_df.to_excel(writer, sheet_name='电源线记录', index=False)
                        log_data_info(logger, "电源线记录表", len(power_wire_df))
                    # 保存压头匹配结果
                    if not terminal_counts_df.empty:
                        terminal_counts_df.to_excel(writer, sheet_name='压头匹配', index=False)
                        log_data_info(logger, "压头匹配表", len(terminal_counts_df))
                    # 保存套管匹配结果
                    if not sleeve_counts_df.empty:
                        sleeve_counts_df.to_excel(writer, sheet_name='套管匹配', index=False)
                        log_data_info(logger, "套管匹配表", len(sleeve_counts_df))
                    # 保存色带用量统计
                    if not color_band_df.empty:
                        color_band_df.to_excel(writer, sheet_name='色带用量', index=False)
                        log_data_info(logger, "色带用量表", len(color_band_df))
                    
                    # 保存并关闭文件
                    writer.close()
                    log_process_step(logger, "输出表生成完成", output_path)
                    if progress_callback:
                        progress_callback("输出表生成完成！", 15, 15)
                    if app and hasattr(app, 'status_var'):
                        app.status_var.set(f"输出表已生成：{output_path}")
                    if app and hasattr(app, 'add_continue_button'):
                        app.add_continue_button()
                    return []
                except Exception as e:
                    log_function_error(logger, "生成输出表", str(e))
                    if app and hasattr(app, 'status_var'):
                        app.status_var.set(f"生成输出表失败: {str(e)}")
                    if app and hasattr(app, 'add_continue_button'):
                        app.add_continue_button()
                    return []
            else:
                log_process_step(logger, "跳过BOM处理", "非自动识别模式或无小母线数据")

        # 统一处理残值数据（无论是否生成BOM都需要处理）
        log_process_step(logger, "处理残值数据")

        # 如果用户指定了残值文件，则更新该文件；否则不保存
        if residual_file:
            residual_manager.save_residual_file()
            log_process_step(logger, "残值文件已更新", residual_file)
        else:
            log_process_step(logger, "未指定残值文件，跳过残值保存")

        # 获取残值汇总信息
        residual_summary = residual_manager.get_residual_summary()
        log_process_step(logger, "残值汇总", residual_summary)

        # 返回空的残值数据（不再生成汇总表）
        merged_residuals = []

        log_function_end(logger, "handle_selection", "处理完成")
        if progress_callback:
            progress_callback("处理完成！", 15, 15)
        if app and hasattr(app, 'status_var'):
            app.status_var.set(f"处理完成！")
        if app and hasattr(app, 'add_continue_button'):
            app.add_continue_button()  # 添加继续处理按钮

        # 返回残值数据
        return merged_residuals

    except Exception as e:
        log_function_error(logger, "handle_selection", str(e))
        if app and hasattr(app, 'status_var'):
            app.status_var.set(f"处理出错: {str(e)}")
        if app and hasattr(app, 'root'):
            app.root.bell()
        import traceback
        traceback.print_exc()  # 打印详细错误信息
        if app and hasattr(app, 'add_continue_button'):
            app.add_continue_button()  # 出错时也添加继续处理按钮
        return []


def merge_residual_lists(residual_list):
    from collections import defaultdict
    merged = defaultdict(lambda: {'物料编码': '', '物料名称': '', '残值': 0.0})
    for item in residual_list:
        if not isinstance(item, dict):
            continue
        key = (item.get('物料编码', ''), item.get('物料名称', ''))
        merged[key]['物料编码'] = item.get('物料编码', '')
        merged[key]['物料名称'] = item.get('物料名称', '')
        merged[key]['残值'] += item.get('残值', 0.0)
    return list(merged.values())


if __name__ == "__main__":
    root = tk.Tk()
    app = ExcelFileSelectorWithContinue(root, callback=lambda *args, **kwargs: handle_selection(*args, **kwargs, app=app, use_multithreading=True))
    root.mainloop()