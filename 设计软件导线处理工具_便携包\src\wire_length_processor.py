# wire_length_processor.py

import pandas as pd
from collections import defaultdict
import numpy as np
import logging

# 导入日志配置
from logger_config import get_wire_length_logger, log_function_start, log_function_end, log_function_error, log_process_step, log_data_info

# 获取导线长度处理日志记录器
logger = get_wire_length_logger()
logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志

# 供外部调用
__all__ = ['process_wire_length', 'add_wire_length', 'add_parallel_wire_length']


def process_wire_length(wire_count_df, wire_length_def_path, is_rotating_cabinet=False):
    """
    处理导线线长统计
    :param wire_count_df: 导线根数统计结果的DataFrame
    :param wire_length_def_path: 线长定义表文件路径
    :param is_rotating_cabinet: 是否为旋转柜（如果是，长度需要×2）
    :return: 处理后的DataFrame
    """
    try:
        log_function_start(logger, "process_wire_length", 旋转柜=is_rotating_cabinet)
        log_data_info(logger, "输入数据", len(wire_count_df))

        # 1. 处理设备类型（起点/终点）列
        log_process_step(logger, "处理设备类型标准化")
        wire_count_df = process_device_type(wire_count_df)

        # 2. 添加单根长度列
        log_process_step(logger, "添加单根长度", wire_length_def_path)
        wire_count_df = add_wire_length(wire_count_df, wire_length_def_path)

        # 3. 添加总长度列
        rotation_factor = "×2" if is_rotating_cabinet else "×1"
        log_process_step(logger, "计算总长度", f"旋转柜系数{rotation_factor}")
        wire_count_df = add_total_length(wire_count_df, is_rotating_cabinet)



        log_function_end(logger, "process_wire_length")
        return wire_count_df

    except Exception as e:
        log_function_error(logger, "process_wire_length", str(e))
        raise


def process_device_type(df):
    """
    处理设备类型（起点/终点）列
    规则：
    1. 单元格中没有'/'的数据，在左侧添加"接地铜排/"
    2. 若颜色/线径标识为"屏蔽线"，则不作任何处理
    """
    df_copy = df.copy()

    # 处理B列：设备类型（起点/终点）
    device_col = '设备类型（起点/终点）'
    color_col = '颜色/线径标识'

    # 创建屏蔽线行的掩码
    shield_mask = df_copy[color_col] == '屏蔽线'

    # 找出不包含'/'且不是屏蔽线的设备类型
    mask = (~df_copy[device_col].str.contains('/', na=False)) & (~shield_mask)

    # 在这些设备类型前添加"接地铜排/"
    df_copy.loc[mask, device_col] = "接地铜排/" + df_copy.loc[mask, device_col]

    return df_copy


def add_wire_length(df, wire_length_def_path):
    """
    添加单根长度列
    规则：
    1. 读取线长定义表
    2. 匹配设备类型的两端与定义表的A、B列
    3. 提取对应行的C列数据作为单根长度
    """
    # 1. 读取线长定义表
    wire_length_df = pd.read_excel(wire_length_def_path)

    # 创建设备组合到长度的映射字典
    length_map = defaultdict(lambda: None)

    # 创建双向映射：A/B和B/A都映射到同一个长度
    for _, row in wire_length_df.iterrows():
        a = row['相对位置A']
        b = row['相对位置B']
        length = row['预估长度']

        # 创建两种键
        key1 = f"{a}/{b}"
        key2 = f"{b}/{a}"

        length_map[key1] = length
        length_map[key2] = length

    # 2. 添加单根长度列
    df['单根长度'] = df['设备类型（起点/终点）'].apply(
        lambda x: length_map.get(x, np.nan)
    )

    return df


def add_total_length(df, is_rotating_cabinet=False):
    """
    添加总长度列
    规则：总长度 = 导线根数 × 单根长度 × (旋转柜系数)
    """
    # 创建副本避免SettingWithCopyWarning
    df_copy = df.copy()

    # 计算总长度
    df_copy['总长度'] = df_copy['导线根数'] * df_copy['单根长度']
    
    # 如果是旋转柜，长度需要乘以2
    if is_rotating_cabinet:
        df_copy['总长度'] = df_copy['总长度'] * 2

    # 处理可能的NaN值（当单根长度缺失时）
    df_copy['总长度'] = df_copy['总长度'].replace(np.nan, 0)

    return df_copy


def add_parallel_wire_length(parallel_df, wire_length_def_path, use_multithreading=True):
    """
    为并线统计表添加单根长度
    :param parallel_df: 并线统计表DataFrame，包含设备类型1、设备类型2等字段
    :param wire_length_def_path: 线长定义表文件路径
    :param use_multithreading: 是否使用多线程处理
    :return: 添加了单根长度1、单根长度2列的DataFrame
    """
    try:
        log_function_start(logger, "add_parallel_wire_length", 多线程=use_multithreading)
        log_data_info(logger, "输入并线统计数据", len(parallel_df))

        if parallel_df.empty:
            log_process_step(logger, "并线统计表为空，跳过处理")
            return parallel_df

        # 创建副本避免修改原数据
        result_df = parallel_df.copy()

        # === 为并线统计表添加单根长度 ===
        if use_multithreading and len(result_df) > 10:  # 数据量较大时使用多线程
            log_process_step(logger, "使用多线程计算单根长度")
            from multithreading_processor import parallel_wire_length_calculation
            result_df = parallel_wire_length_calculation(result_df, wire_length_def_path)
        else:
            log_process_step(logger, "使用单线程计算单根长度")
            # 需要构造设备类型（起点/终点）列，便于add_wire_length使用
            def make_device_type(row, idx):
                if idx == 1:
                    return row['设备类型1']
                else:
                    return row['设备类型2']

            log_process_step(logger, "构造设备类型列")
            result_df['设备类型（起点/终点）1'] = result_df.apply(lambda row: make_device_type(row, 1), axis=1)
            result_df['设备类型（起点/终点）2'] = result_df.apply(lambda row: make_device_type(row, 2), axis=1)

            # 计算单根长度1
            log_process_step(logger, "计算单根长度1")
            df1 = result_df.copy()
            df1['设备类型（起点/终点）'] = df1['设备类型（起点/终点）1']
            df1 = add_wire_length(df1, wire_length_def_path)
            result_df['单根长度1'] = df1['单根长度']

            # 计算单根长度2
            log_process_step(logger, "计算单根长度2")
            df2 = result_df.copy()
            df2['设备类型（起点/终点）'] = df2['设备类型（起点/终点）2']
            df2 = add_wire_length(df2, wire_length_def_path)
            result_df['单根长度2'] = df2['单根长度']

            # 清理临时列
            if '设备类型（起点/终点）1' in result_df.columns:
                result_df = result_df.drop(columns=['设备类型（起点/终点）1', '设备类型（起点/终点）2'])

        # === 连续多芯线分组置零逻辑 ===
        log_process_step(logger, "处理连续多芯线分组置零逻辑")
        result_df = _process_multi_core_wire_grouping(result_df)

        log_function_end(logger, "add_parallel_wire_length")
        log_data_info(logger, "处理后并线统计数据", len(result_df))
        return result_df

    except Exception as e:
        log_function_error(logger, "add_parallel_wire_length", str(e))
        raise


def _process_multi_core_wire_grouping(result_df):
    """
    处理连续多芯线分组置零逻辑
    :param result_df: 包含单根长度1和单根长度2的DataFrame
    :return: 处理后的DataFrame
    """
    result_df['分组键'] = result_df['并线组号'].str.split(':').str[0]
    grouped = result_df.groupby('分组键')
    multi_core_colors = ['棕($)', '蓝($)', '黑($)']

    for group_key, group_df in grouped:
        idxs = list(group_df.index)
        n = len(idxs)
        i = 0
        while i < n:
            # 判断当前行是否为多芯线颜色
            color1 = str(group_df.at[idxs[i], '颜色/线径标识1'])
            color2 = str(group_df.at[idxs[i], '颜色/线径标识2'])
            if any(mc in color1 for mc in multi_core_colors) or any(mc in color2 for mc in multi_core_colors):
                # 连续多芯线分组
                group_indices = [idxs[i]]
                color_set = set()
                if any(mc in color1 for mc in multi_core_colors):
                    color_set.add(color1)
                if any(mc in color2 for mc in multi_core_colors):
                    color_set.add(color2)
                j = i + 1
                while j < n:
                    c1 = str(group_df.at[idxs[j], '颜色/线径标识1'])
                    c2 = str(group_df.at[idxs[j], '颜色/线径标识2'])
                    # 只要是多芯线颜色且未出现过的颜色就加入本组
                    added = False
                    if any(mc in c1 for mc in multi_core_colors) and c1 not in color_set:
                        color_set.add(c1)
                        added = True
                    if any(mc in c2 for mc in multi_core_colors) and c2 not in color_set:
                        color_set.add(c2)
                        added = True
                    if added:
                        group_indices.append(idxs[j])
                        j += 1
                    else:
                        break
                # 只保留本组第一个，其余置0
                for k in range(1, len(group_indices)):
                    result_df.at[group_indices[k], '单根长度1'] = 0
                    result_df.at[group_indices[k], '单根长度2'] = 0
                i = j
            else:
                i += 1

    # 清理临时列
    result_df = result_df.drop(columns=['分组键'])
    return result_df


